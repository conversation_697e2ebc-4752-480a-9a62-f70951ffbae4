html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
  font-family: 'Inter', sans-serif;
}

/* Home page specific styles */
.home-body {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow-x: hidden;
}

.home-main {
  height: 100vh;
  padding: 0;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Hero Section Styles */
.hero-container {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.hero-content {
  text-align: center;
  z-index: 10;
  position: relative;
  max-width: 600px;
  padding: 2rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
  font-weight: 300;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-action {
  animation: fadeInUp 1s ease-out 0.4s both;
}

.btn-new-story {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2.5rem;
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
}

.hero-action form {
  display: inline-block;
}

.btn-new-story:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
  color: white;
  text-decoration: none;
}

.btn-new-story:active {
  transform: translateY(-1px);
}

.btn-icon {
  font-size: 1.5rem;
  animation: sparkle 2s ease-in-out infinite;
}

/* Floating Background Elements */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.element-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.element-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.element-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.2) rotate(180deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .btn-new-story {
    padding: 0.875rem 2rem;
    font-size: 1.1rem;
  }

  .hero-content {
    padding: 1rem;
  }
}

/* Story Creator Styles */
.card {
  border: none;
  border-radius: 15px;
}

.card-header {
  border-radius: 15px 15px 0 0 !important;
  padding: 1.5rem;
}

.form-control {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control-lg {
  padding: 0.75rem 1rem;
  font-size: 1.1rem;
}

.btn {
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(45deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.badge {
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
}

/* Step 2 Styles */
.outline-container {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
}

.generated-outline {
  font-family: 'Georgia', serif;
  line-height: 1.8;
  counter-reset: outline-counter;
}

.outline-item-container {
  counter-increment: outline-counter;
}

.outline-item-content::before {
  content: counter(outline-counter);
  color: #ffffff;
  font-weight: bold;
  margin-right: 0.75rem;
  background: linear-gradient(45deg, #007bff, #0056b3);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
  flex-shrink: 0;
}

.outline-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid #dee2e6;
  font-size: 1.05rem;
}

.outline-item:last-child {
  border-bottom: none;
}

.outline-item:hover {
  background-color: #ffffff;
  border-radius: 5px;
  padding-left: 1rem;
  transition: all 0.3s ease;
}

.card.bg-light .card-body {
  padding: 1rem;
}

.card.bg-light .card-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.card.bg-light .card-text {
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 0;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Timeline Outline Styles */
.generated-outline {
  position: relative;
  padding-left: 2rem;
}

/* Timeline vertical line */
.generated-outline::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, #007bff, #28a745);
  border-radius: 2px;
  z-index: 1;
}

.outline-item-container {
  position: relative;
  margin-bottom: 1.5rem;
}

/* Timeline dots */
.outline-item-container::before {
  content: '';
  position: absolute;
  left: -1.75rem;
  top: 1rem;
  width: 12px;
  height: 12px;
  background: #007bff;
  border: 3px solid #ffffff;
  border-radius: 50%;
  box-shadow: 0 0 0 3px #007bff;
  z-index: 2;
  transition: all 0.3s ease;
}

.outline-item-container:hover::before {
  background: #28a745;
  box-shadow: 0 0 0 3px #28a745;
  transform: scale(1.2);
}

.outline-item-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: #ffffff;
  border: 2px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-left: 0.5rem;
}

.outline-item-wrapper:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.15);
  transform: translateX(4px);
}

.outline-item-controls {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  opacity: 0.8;
  transition: opacity 0.3s ease;
  min-width: 80px;
}

.outline-item-wrapper:hover .outline-item-controls {
  opacity: 1;
}

.outline-item-controls .btn {
  padding: 0.25rem 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  white-space: nowrap;
  min-width: 70px;
}

.outline-item-content {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}



.outline-item-text {
  flex: 1;
  font-family: 'Georgia', serif;
  font-size: 1.05rem;
  line-height: 1.6;
  padding: 0.5rem;
  border-radius: 6px;
  background: transparent;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.outline-item-text:hover {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

.outline-item-edit {
  flex: 1;
  border: 2px solid #007bff;
  background: #ffffff;
  font-family: 'Georgia', serif;
  font-size: 1.05rem;
  line-height: 1.6;
  padding: 0.5rem;
  border-radius: 6px;
  resize: none;
  overflow: hidden;
  min-height: 60px;
  transition: all 0.3s ease;
}

.outline-item-edit:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.new-item-textarea {
  flex: 1;
  border: 2px solid #28a745;
  background: #ffffff;
  font-family: 'Georgia', serif;
  font-size: 1.05rem;
  line-height: 1.6;
  padding: 0.5rem;
  border-radius: 6px;
  resize: none;
  overflow: hidden;
  min-height: 60px;
  transition: all 0.3s ease;
}

.new-item-textarea:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.hidden {
  display: none !important;
}

.add-item-section {
  text-align: center;
  padding: 0.75rem 0;
  opacity: 0.9;
  transition: all 0.3s ease;
  position: relative;
  margin-left: 0.5rem;
}

.add-item-section:hover {
  opacity: 1;
}

/* Timeline dots for add sections */
.add-item-section::before {
  content: '';
  position: absolute;
  left: -2.25rem;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: #28a745;
  border: 2px solid #ffffff;
  border-radius: 50%;
  box-shadow: 0 0 0 2px #28a745;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.add-item-section:hover::before {
  opacity: 1;
  transform: translateY(-50%) scale(1.3);
}

.add-item-top {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
}

.add-item-between {
  margin: 1rem 0;
}

.add-item-bottom {
  margin-top: 1.5rem;
  padding-top: 1rem;
}

.add-between-btn {
  font-size: 0.85rem;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  border-width: 2px;
}

.add-between-btn:hover {
  background-color: #28a745;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.add-item-btn, .delete-item-btn, .edit-item-btn {
  transition: all 0.3s ease;
}

.add-item-btn:hover {
  background-color: #28a745;
  color: white;
  transform: scale(1.1);
}

.delete-item-btn:hover {
  background-color: #dc3545;
  color: white;
  transform: scale(1.1);
}

.edit-item-btn:hover {
  background-color: #007bff;
  color: white;
  transform: scale(1.1);
}

.new-item-editing {
  background-color: #f8fff9;
  border: 2px dashed #28a745;
  border-radius: 12px;
  margin: 1rem 0;
  position: relative;
  margin-left: 0.5rem;
}

/* Special timeline dot for new items */
.new-item-editing::before {
  content: '';
  position: absolute;
  left: -2.25rem;
  top: 1rem;
  width: 14px;
  height: 14px;
  background: #28a745;
  border: 3px solid #ffffff;
  border-radius: 50%;
  box-shadow: 0 0 0 3px #28a745;
  z-index: 2;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 3px #28a745;
  }
  50% {
    box-shadow: 0 0 0 6px rgba(40, 167, 69, 0.5);
  }
  100% {
    box-shadow: 0 0 0 3px #28a745;
  }
}

.new-item-editing .outline-item-wrapper {
  background-color: transparent;
  border: none;
  box-shadow: none;
}

/* Improved button styles */
.btn-lg {
  padding: 0.75rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.btn-success {
  background: linear-gradient(45deg, #28a745, #20c997);
  border: none;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
  background: linear-gradient(45deg, #218838, #1e7e34);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-outline-primary:hover {
  background-color: #007bff;
  border-color: #007bff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  transform: translateY(-1px);
}

/* Better spacing for button groups */
.d-flex.justify-content-between .btn {
  margin: 0 0.25rem;
}

/* Generated paragraph styles */
.generated-paragraph-container {
  margin-top: 1rem;
  margin-left: 2.5rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.generated-paragraph-container::before {
  content: '';
  position: absolute;
  left: -2rem;
  top: 1rem;
  width: 8px;
  height: 8px;
  background: #28a745;
  border: 2px solid #ffffff;
  border-radius: 50%;
  box-shadow: 0 0 0 2px #28a745;
  z-index: 2;
}

.generated-paragraph-header {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.generated-paragraph-header i {
  color: #28a745;
}

.generated-paragraph-content {
  font-size: 1rem;
  line-height: 1.6;
  color: #212529;
  background: #ffffff;
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid #28a745;
  font-family: 'Georgia', serif;
  text-align: justify;
}

.generate-btn {
  background: linear-gradient(45deg, #28a745, #20c997);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.generate-btn:hover:not(:disabled) {
  background: linear-gradient(45deg, #218838, #1ea085);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
  color: white;
}

.generate-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.generate-btn .fa-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive improvements */
@media (max-width: 768px) {
  /* Adjust timeline for mobile */
  .generated-outline {
    padding-left: 1.5rem;
  }

  .generated-outline::before {
    left: 0.75rem;
    width: 2px;
  }

  .outline-item-container::before {
    left: -1.25rem;
    width: 10px;
    height: 10px;
    border-width: 2px;
  }

  .add-item-section::before {
    left: -1.75rem;
    width: 6px;
    height: 6px;
  }

  .new-item-editing::before {
    left: -1.75rem;
    width: 12px;
    height: 12px;
    border-width: 2px;
  }

  .outline-item-wrapper {
    margin-left: 0.25rem;
    padding: 0.75rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .add-item-section {
    margin-left: 0.25rem;
  }

  .new-item-editing {
    margin-left: 0.25rem;
  }

  .outline-item-controls {
    opacity: 1;
    flex-direction: row;
    gap: 0.5rem;
    min-width: auto;
  }

  .outline-item-controls .btn {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    min-width: 60px;
  }

  .outline-item-content::before {
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
  }

  .add-between-btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .btn-lg {
    padding: 0.6rem 1.5rem;
    font-size: 1rem;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .d-flex.justify-content-between > div {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
  }

  /* Generated paragraphs on mobile */
  .generated-paragraph-container {
    margin-left: 1rem;
    padding: 0.75rem;
  }

  .generated-paragraph-container::before {
    left: -1.5rem;
    width: 6px;
    height: 6px;
  }

  .generated-paragraph-content {
    font-size: 0.9rem;
    padding: 0.75rem;
  }
}

/* Better visual hierarchy */
.card-header h3 {
  color: #495057;
  font-weight: 600;
}

.outline-container {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.outline-container:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

