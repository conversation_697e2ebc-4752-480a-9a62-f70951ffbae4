using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Options;
using AICreator.Models;

namespace AICreator.Services
{
    public class OpenRouterService
    {
        private readonly HttpClient _httpClient;
        private readonly OpenRouterConfig _config;

        public OpenRouterService(HttpClient httpClient, IOptions<OpenRouterConfig> config)
        {
            _httpClient = httpClient;
            _config = config.Value;
        }

        public async Task<string> GenerateStoryOutlineAsync(string genre, string characters, string plotOutline)
        {
            var prompt = $@"Na podstawie poniższych informacji wygeneruj szczegółowy konspekt opowiadania w formie punktów, które będą odpowiadać paragrafom docelowego tekstu. Każdy punkt powinien opisywać konkretną scenę lub wydarzenie.

Gatunek: {genre}
Bohaterowie: {characters}
Ogólny zarys: {plotOutline}

Wygeneruj konspekt składający się z kilkunastu punktów, gdzie każdy punkt to jeden paragraf przyszłego opowiadania. Każdy punkt powinien być konkretny i opisywać konkretną akcję, dialog lub wydarzenie.
Punkty powinny być oddzielone przejściem do nowej linii

Format odpowiedzi:
[Opis pierwszej sceny/wydarzenia]
[Opis drugiej sceny/wydarzenia]
...

Odpowiedz tylko konspektem, bez dodatkowych komentarzy.";

            var requestBody = new
            {
                model = _config.Model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = 1000,
                temperature = 0.7
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_config.ApiKey}");
            _httpClient.DefaultRequestHeaders.Add("HTTP-Referer", "http://localhost:5270");
            _httpClient.DefaultRequestHeaders.Add("X-Title", "AICreator");

            var response = await _httpClient.PostAsync($"{_config.BaseUrl}/chat/completions", content);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"OpenRouter API error: {response.StatusCode} - {errorContent}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseJson = JsonSerializer.Deserialize<JsonElement>(responseContent);
            
            var outline = responseJson
                .GetProperty("choices")[0]
                .GetProperty("message")
                .GetProperty("content")
                .GetString();

            return outline ?? "Nie udało się wygenerować konspektu.";
        }

        public async Task<string> GenerateParagraphAsync(string genre, string characters, string plotOutline, string currentOutlinePoint, string? previousParagraphs = null)
        {
            var contextPart = string.IsNullOrEmpty(previousParagraphs)
                ? "To jest pierwszy paragraf opowiadania."
                : $"Kontekst z poprzednich paragrafów:\n{previousParagraphs}";

            var prompt = $@"Na podstawie poniższych informacji napisz jeden paragraf opowiadania (około 3-5 zdań) który realizuje podany punkt konspektu.

Gatunek: {genre}
Bohaterowie: {characters}
Ogólny zarys fabuły: {plotOutline}

{contextPart}

Punkt konspektu do zrealizowania: {currentOutlinePoint}

Napisz jeden spójny paragraf który naturalnie kontynuuje opowiadanie i realizuje podany punkt konspektu. Paragraf powinien być napisany w stylu odpowiednim dla gatunku i zawierać konkretne akcje, dialogi lub opisy zgodne z punktem konspektu.

Odpowiedz tylko treścią paragrafu, bez dodatkowych komentarzy.";

            var requestBody = new
            {
                model = _config.Model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = 300,
                temperature = 0.8
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_config.ApiKey}");
            _httpClient.DefaultRequestHeaders.Add("HTTP-Referer", "http://localhost:5270");
            _httpClient.DefaultRequestHeaders.Add("X-Title", "AICreator");

            var response = await _httpClient.PostAsync($"{_config.BaseUrl}/chat/completions", content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"OpenRouter API error: {response.StatusCode} - {errorContent}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseJson = JsonSerializer.Deserialize<JsonElement>(responseContent);

            var paragraph = responseJson
                .GetProperty("choices")[0]
                .GetProperty("message")
                .GetProperty("content")
                .GetString();

            return paragraph ?? "Nie udało się wygenerować paragrafu.";
        }
    }
}
